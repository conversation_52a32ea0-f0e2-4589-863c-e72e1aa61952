'use client';

import { useEffect } from 'react';
import { useSearchParams } from 'next/navigation';
import { useAnalytics } from '@/lib/analytics-manager';

// ✅ Define local types for traffic data
interface TrafficData {
  utm_campaign?: string | null;
  utm_term?: string | null;
  utm_source?: string | null;
  utm_medium?: string | null;
  referrer: string;
  landing_page: string;
}

interface GoogleAdsData {
  utm_campaign?: string | null;
  utm_term?: string | null;
  utm_source?: string | null;
  utm_medium?: string | null;
  referrer: string;
  landing_page: string;
}

// ✅ Proper Clarity types without using 'any'
interface ClarityAPI {
  (command: 'setTag', key: string, value: string): void;
  (command: 'event', eventName: string): void;
}

// ✅ Safe Clarity access function - no 'any' types
function getClarityAPI(): ClarityAPI | undefined {
  if (typeof window === 'undefined') return undefined;

  // ✅ Safe way to access clarity without type assertion
  return (window as typeof window & { clarity?: ClarityAPI }).clarity;
}

export default function TrafficSourceTracker(): null {
  const searchParams = useSearchParams();
  const { trackEvent, isClarityReady } = useAnalytics();

  useEffect(() => {
    // Only proceed if Clarity is ready
    if (!isClarityReady()) {
      return;
    }

    // Get UTM parameters and referrer
    const utmSource = searchParams.get('utm_source');
    const utmMedium = searchParams.get('utm_medium');
    const utmCampaign = searchParams.get('utm_campaign');
    const utmTerm = searchParams.get('utm_term');
    const ref = document.referrer;
    const landingPage = window.location.pathname;

    // Base tracking data
    const baseData = {
      utm_source: utmSource,
      utm_medium: utmMedium,
      utm_campaign: utmCampaign,
      utm_term: utmTerm,
      referrer: ref,
      landing_page: landingPage
    };

    // ✅ Safe Clarity tracking function
    const trackWithClarity = (
      tags: Record<string, string>,
      eventName: string
    ): void => {
      try {
        const clarity = getClarityAPI();
        if (clarity) {
          // Set all tags
          Object.entries(tags).forEach(([key, value]) => {
            if (value) {
              clarity('setTag', key, value);
            }
          });
          // Fire the event
          clarity('event', eventName);
        }
      } catch (error) {
        console.warn('Clarity tracking error:', error);
      }
    };

    // Reddit traffic tracking
    if (utmSource === 'reddit' || ref.includes('reddit.com')) {
      const redditData: TrafficData = {
        ...baseData,
        utm_source: utmSource || 'reddit',
        utm_medium: utmMedium || 'social'
      };

      // Track with Google Analytics
      trackEvent('traffic_source_reddit', {
        event_category: 'traffic_source',
        event_label: 'reddit',
        ...redditData
      });

      // Track with Clarity - no 'any' types
      trackWithClarity(
        {
          traffic_source: 'reddit',
          utm_source: redditData.utm_source || '',
          utm_medium: redditData.utm_medium || '',
          utm_campaign: utmCampaign || ''
        },
        'reddit_visitor_landed'
      );
    }

    // Google Ads traffic tracking
    else if (utmSource === 'google' && utmMedium === 'cpc') {
      const googleAdsData: GoogleAdsData = {
        ...baseData,
        utm_source: 'google',
        utm_medium: 'cpc'
      };

      trackEvent('traffic_source_google_ads', {
        event_category: 'traffic_source',
        event_label: 'google_ads',
        ...googleAdsData
      });

      trackWithClarity(
        {
          traffic_source: 'google_ads',
          utm_source: 'google',
          utm_medium: 'cpc',
          utm_campaign: utmCampaign || '',
          utm_term: utmTerm || ''
        },
        'google_ads_visitor_landed'
      );
    }

    // Organic Google traffic
    else if (ref.includes('google.com') && !utmMedium) {
      const organicData = {
        ...baseData,
        utm_source: 'google',
        utm_medium: 'organic'
      };

      trackEvent('traffic_source_organic', {
        event_category: 'traffic_source',
        event_label: 'google_organic',
        ...organicData
      });

      trackWithClarity(
        {
          traffic_source: 'google_organic'
        },
        'organic_google_visitor_landed'
      );
    }

    // Direct traffic
    else if (!ref && !utmSource) {
      const directData = {
        ...baseData,
        utm_source: 'direct',
        utm_medium: 'none'
      };

      trackEvent('traffic_source_direct', {
        event_category: 'traffic_source',
        event_label: 'direct',
        ...directData
      });

      trackWithClarity(
        {
          traffic_source: 'direct'
        },
        'direct_visitor_landed'
      );
    }

    // Other referral traffic
    else if (ref && !utmSource) {
      const referralData = {
        ...baseData,
        utm_source: 'referral',
        utm_medium: 'referral'
      };

      trackEvent('traffic_source_referral', {
        event_category: 'traffic_source',
        event_label: 'referral',
        ...referralData
      });

      trackWithClarity(
        {
          traffic_source: 'referral'
        },
        'referral_visitor_landed'
      );
    }
  }, [searchParams, trackEvent, isClarityReady]);

  return null; // This component doesn't render anything
}
