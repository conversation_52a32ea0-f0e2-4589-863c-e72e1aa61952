import { useCallback } from 'react';
import { SERVICE_CREDIT_COSTS } from '@/lib/credit-costs';
import { useFeature } from './FeatureContext';
import { FeatureType } from '@/app/types/featureTypes';
import {
  ServiceType,
  ResumeAnalysisResult,
  FeatureResult
} from '@/app/types/globalTypes';
import { toast } from '@/hooks/use-toast';

// Import API functions
import { performSkillsGapAnalysis } from '@/components/utils/skillsGapAnalysis';
// import { performMockInterview } from '@/app/utils/mockInterview';
import { performCareerMatchingAnalysis } from '@/app/utils/careerMatchingAnalysis';
// import { performCareerCoaching } from '@/app/utils/careerCoaching';
import { performMarketTrendsAnalysis } from '@/app/utils/marketTrends';
import { performSkillImprovementsAnalysis } from '@/app/utils/learningResources';
import { generateCoverLetter } from '@/app/utils/coverLetterGeneration';
// import { TailoredResume } from '@/app/types/globalTypes';

// Used when parsing API response
interface CreditResponse {
  remainingCredits: number;
  success?: boolean;
  used?: number;
  error?: string;
}

const FEATURE_TO_SERVICE_TYPE: Record<FeatureType, ServiceType> = {
  ats: 'ATS_ANALYSIS',
  skillGap: 'SKILL_GAP_ANALYSIS',
  skillsGap: 'SKILL_GAP_ANALYSIS',
  careerMatching: 'CAREER_MATCHING',
  mockInterview: 'MOCK_INTERVIEW',
  learningResources: 'LEARNING_RESOURCES',
  marketTrends: 'MARKET_TRENDS',
  resumeImprovement: 'RESUME_IMPROVEMENT',
  coverLetter: 'COVER_LETTER',
  careerCoach: 'CAREER_COACHING',
  recruitmentAgencies: 'RECRUITMENT_AGENCIES'
};

// Note: We're using our own mapping that matches the one in featureTypes.ts
// This ensures consistency with the storage keys used in the Dashboard component

// Map from FeatureType to storage key
const FEATURE_TO_STORAGE_KEY: Record<FeatureType, string> = {
  ats: 'SimpleATS',
  skillGap: 'SkillsGap',
  skillsGap: 'SkillsGap',
  careerMatching: 'CareerMatching',
  mockInterview: 'MockInterview',
  learningResources: 'LearningResources',
  marketTrends: 'MarketTrends',
  resumeImprovement: 'ResumeImprovement',
  coverLetter: 'CoverLetter',
  careerCoach: 'CareerCoach',
  recruitmentAgencies: 'RecruitmentAgencies'
};

export const useUnifiedFeatureHandlers = () => {
  const {
    selectedJob,
    selectedResume,
    setFeatureResult,
    setFeatureError,
    setFeatureLoading,
    openFeatureDialog,
    setActiveFeature
  } = useFeature();

  // Check if user has enough credits for a feature
  const checkCredits = useCallback(
    async (feature: ServiceType): Promise<boolean> => {
      console.log(`Checking credits for feature: ${feature}`);

      const cost = SERVICE_CREDIT_COSTS[feature];
      if (!cost) {
        console.error(`No credit cost defined for feature: ${feature}`);
        return false;
      }

      // Get credits from the API directly
      try {
        // First, check if we have enough credits by making a GET request
        const creditsResponse = await fetch(
          `${process.env.NEXT_PUBLIC_URL || ''}/api/credits`
        );
        if (!creditsResponse.ok) {
          throw new Error('Failed to fetch credits');
        }

        const creditsData = await creditsResponse.json();
        const credits = creditsData.credits || 0;

        console.log(`Current credits: ${credits}, Required: ${cost}`);

        if (credits < cost) {
          toast({
            title: 'Insufficient Credits',
            description: `You need ${cost} credits to use this feature. Available: ${credits}`,
            variant: 'destructive'
          });
          return false;
        }
      } catch (error) {
        console.error('Error fetching credits:', error);
        // Continue with the credit deduction attempt
      }

      try {
        // Check if we're in development mode with a port mismatch (CORS issue)
        const isDevelopment = process.env.NODE_ENV === 'development';
        const currentUrl = window.location.href;
        const apiUrl = `${process.env.NEXT_PUBLIC_URL}/api/credits/purchase/use`;
        const isCorsIssue =
          isDevelopment && new URL(currentUrl).port !== new URL(apiUrl).port;

        if (isCorsIssue) {
          console.warn(
            'Detected potential CORS issue due to port mismatch in development'
          );
          console.warn('Current URL:', currentUrl, 'API URL:', apiUrl);
          console.warn('Bypassing credit check in development mode');

          // In development with CORS issues, bypass credit check
          // This is only for development convenience
          if (isDevelopment) {
            // Mock credit deduction in development
            // await updateCredits(credits - cost);
            return true;
          }
        }

        const response = await fetch(apiUrl, {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({ serviceType: feature })
        });

        // Clone the response so we can read it multiple times if needed
        const responseClone = response.clone();

        if (!response.ok) {
          let errorMessage = 'Failed to use credits';
          try {
            const errorData = await responseClone.json();
            console.error('Failed to use credits:', errorData);
            errorMessage = errorData.error || errorMessage;
          } catch (e) {
            console.error('Error parsing error response:', e);
          }

          toast({
            title: 'Credit Deduction Failed',
            description: errorMessage,
            variant: 'destructive'
          });

          return false;
        }

        try {
          // Process the response and update the UI
          const result = (await response.json()) as CreditResponse;
          console.log('Credit deduction successful:', result);

          // Update credits and notify components about the credit update
          if (result.remainingCredits !== undefined) {
            // Store the updated credits in localStorage for components that rely on it
            localStorage.setItem('credits', result.remainingCredits.toString());

            // Dispatch both storage and custom events to trigger UI updates
            window.dispatchEvent(new Event('storage'));
            window.dispatchEvent(
              new CustomEvent('credit-update', {
                detail: { credits: result.remainingCredits }
              })
            );

            console.log(`Credits updated: ${result.remainingCredits}`);

            // Show success toast
            toast({
              title: 'Credits Deducted',
              description: `${result.used} credits used. Remaining: ${result.remainingCredits}`,
              duration: 3000
            });

            // Fetch fresh credits from the API to ensure all components are in sync
            fetch(`${process.env.NEXT_PUBLIC_URL || ''}/api/credits`)
              .then((response) => response.json())
              .then((data) => {
                if (data.credits !== undefined) {
                  console.log('Credits refreshed from API:', data.credits);
                }
              })
              .catch((error) => {
                console.error('Error fetching updated credits:', error);
              });
          }

          return true;
        } catch (parseError) {
          console.error('Error parsing credit response:', parseError);

          // If we can't parse the response but it was successful, still return true
          return true;
        }
      } catch (error) {
        console.error('Failed to use credits:', error);

        // If we're in development mode, allow the operation to proceed
        // This is only for development convenience
        if (process.env.NODE_ENV === 'development') {
          console.warn('Development mode: Bypassing credit check after error');
          // Mock credit deduction in development
          // await updateCredits(credits - cost);
          return true;
        }

        return false;
      }
    },
    []
  );

  // Check if user has selected a resume and job
  // Commented out as it's not currently used
  /* const checkSelectedItems = useCallback(() => {
    if (!selectedResume || !selectedJob) {
      throw new Error('Please select both a resume and a job to proceed');
    }

    if (!selectedJob.description) {
      throw new Error('Job description is missing');
    }

    if (!selectedResume.analysis_result) {
      throw new Error('Resume analysis result is not available');
    }

    return {
      resume: selectedResume,
      job: selectedJob
    };
  }, [selectedResume, selectedJob]); */

  // Generic function to activate a feature
  const activateFeature = useCallback(
    async <R extends FeatureResult[keyof FeatureResult]>(
      featureType: FeatureType,
      apiFunction: (
        resumeData: ResumeAnalysisResult | Record<string, unknown>,
        jobDescription: string
      ) => Promise<R>
    ): Promise<R | null> => {
      try {
        // Set loading state and open dialog
        setFeatureLoading(featureType, true);
        setFeatureError(featureType, null);
        setActiveFeature(featureType);
        openFeatureDialog(featureType);

        // Check if user has selected a resume and job
        if (!selectedResume || !selectedJob) {
          throw new Error('Please select both a resume and a job to proceed');
        }

        if (!selectedJob.description) {
          throw new Error('Job description is missing');
        }

        if (!selectedResume.analysis_result) {
          throw new Error('Resume analysis result is not available');
        }

        const resume = selectedResume;
        const job = selectedJob;

        // Check if result is cached in localStorage
        const storageKey = `${FEATURE_TO_STORAGE_KEY[featureType]}_${resume.id}_${job.id}`;
        const cachedResult = localStorage.getItem(storageKey);

        let result: R;

        if (cachedResult) {
          // Use the cached result and skip API call / credit deduction
          console.log(`Using cached result for ${featureType}`);
          result = JSON.parse(cachedResult) as R;
        } else {
          // Only charge credits and call the API when no cached result exists
          if (!(await checkCredits(FEATURE_TO_SERVICE_TYPE[featureType]))) {
            throw new Error('Insufficient credits');
          }

          console.log(`Making API call for ${featureType}...`);
          result = await apiFunction(resume.analysis_result, job.description);

          // Cache the result for future use
          localStorage.setItem(storageKey, JSON.stringify(result));

          // Trigger storage event so other components update
          window.dispatchEvent(new Event('storage'));
        }

        // Set the result
        setFeatureResult(featureType, result); // ✅ type-checked
        return result;
      } catch (error: unknown) {
        console.error(`${featureType} failed:`, error);
        const errorMessage =
          error instanceof Error ? error.message : JSON.stringify(error);
        // Cast errorMessage to string to satisfy TypeScript and avoid complex union type
        setFeatureError(featureType, errorMessage as string);
        // Don't throw the error, just display it in the dialog
        return null;
      } finally {
        setFeatureLoading(featureType, false);
        setActiveFeature(null);
      }
    },
    [
      checkCredits,
      selectedResume,
      selectedJob,
      openFeatureDialog,
      setActiveFeature,
      setFeatureError,
      setFeatureLoading,
      setFeatureResult
    ]
  );

  // ATS Analysis
  const performATSAnalysis = useCallback(async () => {
    return activateFeature(
      'ats',
      async (
        resumeData: ResumeAnalysisResult | Record<string, unknown>,
        jobDescription: string
      ) => {
        try {
          // Make a real API call to the ATS analysis endpoint
          console.log('Making API call to ATS analysis endpoint');

          const response = await fetch('/api/ats-analysis', {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json'
            },
            body: JSON.stringify({
              resume: resumeData,
              jobDescription: jobDescription
            })
          });

          if (!response.ok) {
            const errorText = await response.text();
            console.error('ATS Analysis API Error:', errorText);
            throw new Error(`ATS Analysis API error: ${response.status}`);
          }

          const result = await response.json();
          console.log('Received ATS Analysis result from API:', result);
          return result;
        } catch (error) {
          console.error('ATS Analysis failed:', error);

          // If the API call fails, we'll fall back to a simplified mock result
          console.warn('Falling back to simplified mock ATS result');

          return {
            overall_match: 65,
            overall_match_percentage: 65,
            section_analysis: {},
            keyword_analysis: { essential: [], preferred: [] },
            skills_analysis: {},
            format_analysis: {},
            keyword_match_percentage: 60,
            skill_match_percentage: 70,
            keyword_matches: [],
            skill_matches: [],
            missing_keywords: [],
            missing_skills: [],
            readability_score: 75,
            recommendations: ['API call failed. Please try again later.']
          };
        }
      }
    );
  }, [activateFeature]);

  // Skills Gap Analysis
  const performSkillsGap = useCallback(async () => {
    return activateFeature(
      'skillsGap',
      (
        resumeData: ResumeAnalysisResult | Record<string, unknown>,
        jobDescription: string
      ) => {
        // Ensure we're passing a ResumeAnalysisResult to performSkillsGapAnalysis
        if ('sections' in resumeData) {
          // If resumeData has sections property, it's already a ResumeAnalysisResult
          return performSkillsGapAnalysis(
            resumeData as ResumeAnalysisResult,
            jobDescription
          );
        } else {
          // Convert Record<string, unknown> to ResumeAnalysisResult if needed
          // This is a simplified conversion - adjust based on your actual data structure
          const convertedData: ResumeAnalysisResult = {
            sections: []
          };
          console.warn(
            'Converting generic data to ResumeAnalysisResult format'
          );
          return performSkillsGapAnalysis(convertedData, jobDescription);
        }
      }
    );
  }, [activateFeature]);

  // Mock Interview
  const performMockInterview = useCallback(async () => {
    // Instead of directly setting dialog open here, use openFeatureDialog from context
    openFeatureDialog('mockInterview');
    return activateFeature(
      'mockInterview',
      (
        resumeData: ResumeAnalysisResult | Record<string, unknown>,
        jobDescription: string
      ) => {
        // We need to create a wrapper function since we can't directly pass performMockInterview
        // due to the type mismatch
        if ('sections' in resumeData) {
          // If resumeData has sections property, it's already a ResumeAnalysisResult
          return import('@/app/utils/mockInterview').then((module) => {
            return module.performMockInterview(
              resumeData as ResumeAnalysisResult,
              jobDescription
            );
          });
        } else {
          // Convert Record<string, unknown> to ResumeAnalysisResult if needed
          const convertedData: ResumeAnalysisResult = {
            sections: []
          };
          console.warn('Converting generic data to CVAnalysisResult format');
          return import('@/app/utils/mockInterview').then((module) => {
            return module.performMockInterview(convertedData, jobDescription);
          });
        }
      }
    );
  }, [activateFeature, openFeatureDialog]);

  // Career Matching
  const performCareerMatching = useCallback(async () => {
    return activateFeature(
      'careerMatching',
      (resumeData: ResumeAnalysisResult | Record<string, unknown>) => {
        // Career Matching only needs the resume data, not the job description
        // Check if resumeData has the required structure
        if ('sections' in resumeData) {
          return performCareerMatchingAnalysis(
            resumeData as ResumeAnalysisResult
          );
        } else {
          // Convert to ResumeAnalysisResult if needed
          const convertedData: ResumeAnalysisResult = {
            sections: []
          };
          console.warn(
            'Converting generic data to ResumeAnalysisResult format'
          );
          return performCareerMatchingAnalysis(convertedData);
        }
      }
    );
  }, [activateFeature]);

  // Cover Letter
  const performCoverLetter = useCallback(async () => {
    return activateFeature(
      'coverLetter',
      (
        resumeData: ResumeAnalysisResult | Record<string, unknown>,
        jobDescription: string
      ) => {
        // Check if resumeData has the required structure
        if ('sections' in resumeData) {
          return generateCoverLetter(
            resumeData as ResumeAnalysisResult,
            jobDescription,
            selectedJob?.company || 'Company'
          ) as unknown as Promise<Record<string, unknown>>;
        } else {
          // Convert to ResumeAnalysisResult if needed
          const convertedData: ResumeAnalysisResult = {
            sections: []
          };
          console.warn(
            'Converting generic data to ResumeAnalysisResult format'
          );
          return generateCoverLetter(
            convertedData,
            jobDescription,
            selectedJob?.company || 'Company'
          ) as unknown as Promise<Record<string, unknown>>;
        }
      }
    );
  }, [activateFeature, selectedJob]);

  // Career Coach
  const performCareerCoach = useCallback(async () => {
    try {
      // Set loading state
      setFeatureLoading('careerCoach', true);
      setFeatureError('careerCoach', null);

      // Check if user has enough credits before proceeding
      console.log('performCareerCoach - Checking credits');
      const hasEnoughCredits = await checkCredits('CAREER_COACHING');
      if (!hasEnoughCredits) {
        console.error('Insufficient credits for Career Coach');
        setFeatureError(
          'careerCoach',
          'Insufficient credits to use AI Career Coach'
        );
        return null;
      }

      // Open the dialog
      console.log('performCareerCoach - Opening dialog');
      openFeatureDialog('careerCoach');

      // Return a dummy result to satisfy the type system
      return {
        answer: 'How can I help with your career questions today?',
        relevant_sections: [],
        confidence_score: 1.0,
        follow_up_questions: [],
        analysis: {},
        recommendations: []
      };
    } catch (error) {
      console.error('Career Coach error:', error);
      setFeatureError(
        'careerCoach',
        error instanceof Error ? error.message : String(error)
      );
      return null;
    } finally {
      setFeatureLoading('careerCoach', false);
    }
  }, [openFeatureDialog, setFeatureLoading, setFeatureError, checkCredits]);

  // Market Trends
  const performMarketTrends = useCallback(async () => {
    return activateFeature(
      'marketTrends',
      (
        resumeData: ResumeAnalysisResult | Record<string, unknown>,
        jobDescription: string
      ) => {
        // Create a wrapper function to handle the type conversion
        if ('sections' in resumeData) {
          return performMarketTrendsAnalysis(
            resumeData as ResumeAnalysisResult,
            jobDescription
          );
        } else {
          // Convert Record<string, unknown> to ResumeAnalysisResult if needed
          const convertedData: ResumeAnalysisResult = {
            sections: []
          };
          console.warn(
            'Converting generic data to ResumeAnalysisResult format'
          );
          return performMarketTrendsAnalysis(convertedData, jobDescription);
        }
      }
    );
  }, [activateFeature]);

  // Learning Resources
  const performLearningResources = useCallback(async () => {
    return activateFeature(
      'learningResources',
      (
        resumeData: ResumeAnalysisResult | Record<string, unknown>,
        jobDescription: string
      ) => {
        // Create a wrapper function to handle the type conversion
        if ('sections' in resumeData) {
          return performSkillImprovementsAnalysis(
            resumeData as ResumeAnalysisResult,
            jobDescription
          );
        } else {
          // Convert Record<string, unknown> to ResumeAnalysisResult if needed
          const convertedData: ResumeAnalysisResult = {
            sections: []
          };
          console.warn(
            'Converting generic data to ResumeAnalysisResult format'
          );
          return performSkillImprovementsAnalysis(
            convertedData,
            jobDescription
          );
        }
      }
    );
  }, [activateFeature]);

  // Resume Improvement
  const performResumeImprovement = useCallback(async () => {
    // Helper function to transform the raw API response to the expected format
    const transformRawResult = (rawResult: unknown) => {
      // Transform before_after_comparison to nested structure if needed
      interface BeforeAfterComparison {
        original?: {
          metrics: {
            readabilityScore?: number;
            keywordDensity?: number;
          };
        };
        enhanced?: {
          metrics: {
            readabilityScore?: number;
            keywordDensity?: number;
          };
        };
        readability_before?: number;
        keyword_matches_before?: number;
        readability_after?: number;
        keyword_matches_after?: number;
      }

      const raw = rawResult as Record<string, unknown>;
      let beforeAfterComparison = raw.before_after_comparison as
        | BeforeAfterComparison
        | undefined;
      if (
        beforeAfterComparison &&
        !beforeAfterComparison.original &&
        !beforeAfterComparison.enhanced
      ) {
        beforeAfterComparison = {
          original: {
            metrics: {
              readabilityScore: beforeAfterComparison.readability_before,
              keywordDensity: beforeAfterComparison.keyword_matches_before
            }
          },
          enhanced: {
            metrics: {
              readabilityScore: beforeAfterComparison.readability_after,
              keywordDensity: beforeAfterComparison.keyword_matches_after
            }
          }
        };
      }

      return {
        enhanced_sections: raw.enhanced_sections || [],
        overall_improvements: raw.overall_improvements || [],
        keyword_additions: raw.keyword_additions || [],
        format_improvements: raw.format_improvements || [],
        before_after_comparison: beforeAfterComparison || {},
        full_enhanced_resume: raw.full_enhanced_resume || ''
      };
    };

    return activateFeature(
      'resumeImprovement',
      async (
        resumeData: ResumeAnalysisResult | Record<string, unknown>,
        jobDescription: string
      ) => {
        // Maximum number of retry attempts
        const MAX_RETRIES = 2;
        let retryCount = 0;
        let lastError: Error | null = null;

        // Ensure resumeData is a string
        const resumeContent =
          typeof resumeData === 'string'
            ? resumeData
            : JSON.stringify(resumeData);

        // Create the request body - use the format expected by the skill-improvement endpoint
        // The SkillImprovementRequest model expects resume, job_description, job, and skills_gap
        const requestBody = {
          resume: resumeContent,
          job_description: jobDescription,
          job: jobDescription, // Required field in the API model
          skills_gap: [] // Optional field with default empty array
        };

        // Retry logic
        while (retryCount <= MAX_RETRIES) {
          try {
            console.log(
              `Making API call for Resume Improvement... (Attempt ${retryCount + 1}/${
                MAX_RETRIES + 1
              })`
            );

            // Make the API call to the existing skill-improvement endpoint
            const response = await fetch('/api/enhance/resume', {
              method: 'POST',
              headers: {
                'Content-Type': 'application/json'
              },
              body: JSON.stringify(requestBody)
            });

            if (!response.ok) {
              const errorText = await response.text();

              console.error('Resume Improvement API Error:', errorText);

              // Specific error handling based on status code
              if (response.status === 429) {
                throw new Error('Rate limit exceeded. Please try again later.');
              } else if (response.status >= 500) {
                throw new Error(
                  "Server error. We're working to fix this issue."
                );
              } else if (response.status === 400) {
                throw new Error(
                  'Invalid request format. Please check your CV and job description.'
                );
              } else {
                throw new Error(
                  `API error (${response.status}): ${errorText.substring(0, 100)}`
                );
              }
            }

            const result = await response.json();
            console.log('Received CV Improvement result from API:', result);

            // Transform the raw API result to the expected format
            return transformRawResult(result);
          } catch (error) {
            lastError =
              error instanceof Error ? error : new Error(String(error));
            console.error(
              `CV Improvement attempt ${retryCount + 1} failed:`,
              lastError
            );

            // Only retry on network errors or 5xx server errors
            if (
              !(error instanceof TypeError) &&
              !lastError.message.includes('Server error')
            ) {
              // Don't retry client errors or other specific errors
              break;
            }

            // Increment retry counter
            retryCount++;

            if (retryCount <= MAX_RETRIES) {
              // Exponential backoff: wait longer between each retry
              const backoffTime = Math.min(
                1000 * Math.pow(2, retryCount),
                8000
              );
              console.log(`Retrying in ${backoffTime}ms...`);
              await new Promise((resolve) => setTimeout(resolve, backoffTime));
            }
          }
        }

        // If we've exhausted all retries, throw the last error
        if (lastError) {
          throw lastError;
        }

        // This should never be reached due to the throw above, but TypeScript needs it
        return null as unknown as Promise<Record<string, unknown>>;
      }
    );
  }, [activateFeature]);

  // Recruitment Agencies
  const performRecruitmentAgencies = useCallback(async () => {
    console.log('performRecruitmentAgencies - Function called');
    try {
      // Set loading state
      console.log('performRecruitmentAgencies - Setting loading state');
      setFeatureLoading('recruitmentAgencies', true);
      setFeatureError('recruitmentAgencies', null);

      // Check if user has enough credits before proceeding
      console.log('performRecruitmentAgencies - Checking credits');
      const hasEnoughCredits = await checkCredits('RECRUITMENT_AGENCIES');
      if (!hasEnoughCredits) {
        console.error('Insufficient credits for Recruitment Agencies');
        setFeatureError(
          'recruitmentAgencies',
          'Insufficient credits to use Recruitment Agencies'
        );
        return null;
      }

      // Open the dialog
      console.log('performRecruitmentAgencies - Opening dialog');
      openFeatureDialog('recruitmentAgencies');

      // No need to make an API call here as the component handles its own data fetching
      console.log('performRecruitmentAgencies - Returning result');

      // Return an empty result - the component will handle its own state
      return {
        agencies: [],
        isLoading: false
      };
    } catch (error) {
      console.error('Recruitment Agencies error:', error);
      setFeatureError(
        'recruitmentAgencies',
        error instanceof Error ? error.message : String(error)
      );
      return null;
    } finally {
      console.log(
        'performRecruitmentAgencies - Setting loading state to false'
      );
      setFeatureLoading('recruitmentAgencies', false);
    }
  }, [openFeatureDialog, setFeatureError, setFeatureLoading, checkCredits]);

  return {
    performATSAnalysis,
    performSkillsGap,
    performMockInterview,
    performCareerMatching,
    performCoverLetter,
    performCareerCoach,
    performMarketTrends,
    performLearningResources,
    performResumeImprovement,
    performRecruitmentAgencies
  };
};
