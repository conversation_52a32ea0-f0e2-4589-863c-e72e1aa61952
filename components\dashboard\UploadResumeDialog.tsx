import React, { useState } from 'react';
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON>ontent,
  <PERSON><PERSON><PERSON>eader,
  DialogTitle,
  DialogDescription
} from '@/components/ui/dialog';
import UploadResumeForm from '@/components/resume/uploadResumeForm';
import { Resume } from '@/app/types/globalTypes';
import { Loader2 } from 'lucide-react';

interface UploadResumeDialogProps {
  isOpen: boolean;
  onClose: () => void;
  onComplete: (resume: Resume) => Promise<void>;
  userId: string;
}

export const UploadResumeDialog: React.FC<UploadResumeDialogProps> = ({
  isOpen,
  onClose,
  onComplete,
  userId
}) => {
  // Add a local loading state to control when the dialog actually closes
  const [isProcessing, setIsProcessing] = useState(false);

  const handleUploadComplete = async (resume: Resume) => {
    try {
      setIsProcessing(true);

      // Call onComplete and await it to ensure the resume is fully processed
      await onComplete(resume);

      // Only close the dialog after onComplete finishes
      onClose();
    } catch (error) {
      console.error('Error processing uploaded CV:', error);
      // Show error to user (could add a toast notification here)
    } finally {
      setIsProcessing(false);
    }
  };

  return (
    <Dialog
      open={isOpen}
      onOpenChange={(open) => {
        // Prevent closing the dialog while processing
        if (!open && isProcessing) {
          return;
        }
        onClose();
      }}
    >
      <DialogContent className="sm:max-w-2xl w-[90vw] max-h-[90vh] overflow-y-auto backdrop-blur-sm bg-hero-bg/90 border border-white/20 text-white">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2 text-[hsl(var(--hero-yellow))] text-xl">
            Upload CV
          </DialogTitle>
          <DialogDescription className="text-slate-300">
            Upload your CV in PDF or DOCX format to get started.
          </DialogDescription>
        </DialogHeader>
        <div className="py-4">
          {isProcessing ? (
            <div className="flex flex-col items-center justify-center py-12">
              <Loader2 className="h-12 w-12 animate-spin text-[hsl(var(--hero-yellow))] mb-4" />
              <p className="text-white">Processing your CV...</p>
              <p className="text-slate-300 text-sm mt-2">
                This may take a moment
              </p>
            </div>
          ) : (
            <UploadResumeForm
              userId={userId}
              onComplete={handleUploadComplete}
            />
          )}
        </div>
      </DialogContent>
    </Dialog>
  );
};
