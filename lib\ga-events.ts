import { GA4_EVENTS, type JobSpaceEventParams } from '@/types/events';

// ===== ONBOARDING JOURNEY EVENTS =====

export const trackOnboardingStart = () =>
  trackEvent(GA4_EVENTS.ONBOARDING_START, {
    event_category: 'engagement',
    user_journey: 'onboarding',
    journey_stage: 'consideration'
  });

export const trackSignupAttempt = (method: string = 'email') =>
  trackEvent(GA4_EVENTS.SIGNUP_ATTEMPT, {
    event_category: 'authentication',
    method,
    user_journey: 'onboarding'
  });

export const trackSignupFailure = (
  method: string = 'email',
  errorType?: string
) =>
  trackEvent(GA4_EVENTS.SIGNUP_FAILURE, {
    event_category: 'error',
    method,
    error_type: errorType || 'signup_failure',
    user_journey: 'onboarding'
  });

// ===== FILE UPLOAD EVENTS =====

export const trackFileUploadStart = (
  fileType: 'job_description' | 'cv_resume',
  fileSizeKb?: number
) =>
  trackEvent(GA4_EVENTS.FILE_UPLOAD_START, {
    event_category: 'engagement',
    file_type: fileType,
    file_size_kb: fileSizeKb,
    user_journey: 'onboarding'
  });

export const trackFileUploadSuccess = (
  fileType: 'job_description' | 'cv_resume',
  fileSizeKb?: number,
  uploadDurationMs?: number
) =>
  trackEvent(GA4_EVENTS.FILE_UPLOAD_SUCCESS, {
    event_category: 'engagement',
    file_type: fileType,
    file_size_kb: fileSizeKb,
    upload_duration_ms: uploadDurationMs,
    user_journey: 'onboarding',
    onboarding_step: fileType === 'job_description' ? 3 : 4
  });

export const trackFileUploadFailure = (
  fileType: 'job_description' | 'cv_resume',
  errorMessage?: string
) =>
  trackEvent(GA4_EVENTS.FILE_UPLOAD_FAILURE, {
    event_category: 'error',
    file_type: fileType,
    error_type: 'upload_failed',
    error_message: errorMessage?.substring(0, 100),
    user_journey: 'onboarding'
  });

export const trackUploadAbandon = (fileType: 'job_description' | 'cv_resume') =>
  trackEvent(GA4_EVENTS.UPLOAD_ABANDON, {
    event_category: 'behavior',
    file_type: fileType,
    user_journey: 'onboarding'
  });

// ===== ANALYSIS EVENTS =====

export const trackAnalysisStart = (analysisType: string) =>
  trackEvent(GA4_EVENTS.ANALYSIS_START, {
    event_category: 'engagement',
    analysis_type: analysisType,
    user_journey: 'onboarding'
  });

export const trackAnalysisComplete = (
  analysisType: string,
  analysisTimeMs?: number,
  userJourney: 'onboarding' | 'dashboard_direct' = 'onboarding'
) =>
  trackEvent(GA4_EVENTS.ANALYSIS_COMPLETE, {
    event_category: 'conversion',
    analysis_type: analysisType,
    analysis_time_ms: analysisTimeMs,
    user_journey: userJourney,
    analysis_success: true
  });

export const trackAnalysisFailure = (
  analysisType: string,
  errorMessage?: string
) =>
  trackEvent(GA4_EVENTS.ANALYSIS_FAILURE, {
    event_category: 'error',
    analysis_type: analysisType,
    error_type: 'analysis_failed',
    error_message: errorMessage?.substring(0, 100),
    analysis_success: false
  });

// ===== CTA & ENGAGEMENT EVENTS =====

export const trackCTAClick = (
  ctaType: string,
  ctaLocation: string,
  ctaText?: string
) =>
  trackEvent(GA4_EVENTS.CTA_CLICK, {
    event_category: 'engagement',
    cta_type: ctaType,
    cta_location: ctaLocation,
    button_text: ctaText,
    journey_stage: 'interest'
  });

export const trackPricingPageView = (userJourney?: string) =>
  trackEvent(GA4_EVENTS.PRICING_PAGE_VIEW, {
    event_category: 'ecommerce',
    page_section: 'pricing',
    user_journey: userJourney || 'onboarding'
  });

export const trackPlanCardView = (planId: string, planType: string) =>
  trackEvent(GA4_EVENTS.PLAN_CARD_VIEW, {
    event_category: 'ecommerce',
    plan_id: planId,
    plan_type: planType
  });

export const trackPlanSelect = (
  planId: string,
  planType: string,
  planValue: number
) =>
  trackEvent(GA4_EVENTS.PLAN_SELECT, {
    event_category: 'ecommerce',
    plan_id: planId,
    plan_type: planType,
    value: planValue,
    currency: 'GBP'
  });

// ===== DASHBOARD JOURNEY EVENTS =====

export const trackDashboardView = (returningUser: boolean = false) =>
  trackEvent(GA4_EVENTS.DASHBOARD_VIEW, {
    event_category: 'engagement',
    user_journey: 'dashboard_direct',
    returning_user: returningUser,
    page_title: 'Dashboard'
  });

export const trackFeatureDiscover = (featureName: string) =>
  trackEvent(GA4_EVENTS.FEATURE_DISCOVER, {
    event_category: 'engagement',
    feature_name: featureName,
    user_journey: 'dashboard_direct'
  });

export const trackFeatureSelect = (
  featureName: string,
  featureLocation: string
) =>
  trackEvent(GA4_EVENTS.FEATURE_SELECT, {
    event_category: 'engagement',
    feature_name: featureName,
    feature_location: featureLocation,
    user_journey: 'dashboard_direct'
  });

export const trackFeatureUseStart = (featureName: string) =>
  trackEvent(GA4_EVENTS.FEATURE_USE_START, {
    event_category: 'engagement',
    feature_name: featureName,
    user_journey: 'dashboard_direct'
  });

export const trackFeatureUseComplete = (
  featureName: string,
  timeSpentSeconds?: number
) =>
  trackEvent(GA4_EVENTS.FEATURE_USE_COMPLETE, {
    event_category: 'conversion',
    feature_name: featureName,
    time_spent_seconds: timeSpentSeconds,
    user_journey: 'dashboard_direct'
  });

export const trackFeatureAbandon = (featureName: string) =>
  trackEvent(GA4_EVENTS.FEATURE_ABANDON, {
    event_category: 'behavior',
    feature_name: featureName,
    user_journey: 'dashboard_direct'
  });

// ===== DOCUMENT & CONTENT EVENTS =====

export const trackDocumentGenerated = (
  documentType: string,
  featureUsed: string
) =>
  trackEvent('document_generated', {
    event_category: 'conversion',
    document_type: documentType,
    feature_name: featureUsed
  });

export const trackDocumentDownload = (
  documentType: string,
  fileFormat: string = 'pdf'
) =>
  trackEvent(GA4_EVENTS.DOCUMENT_DOWNLOAD, {
    event_category: 'engagement',
    document_type: documentType,
    file_format: fileFormat,
    journey_stage: 'retention'
  });

export const trackDocumentShare = (documentType: string, shareMethod: string) =>
  trackEvent(GA4_EVENTS.DOCUMENT_SHARE, {
    event_category: 'engagement',
    document_type: documentType,
    method: shareMethod
  });

// ===== SUPPORT & HELP EVENTS =====

export const trackHelpView = (helpTopic: string, helpType: string = 'faq') =>
  trackEvent(GA4_EVENTS.HELP_VIEW, {
    event_category: 'support',
    help_type: helpType,
    help_topic: helpTopic
  });

export const trackSiteSearch = (searchTerm: string, resultCount?: number) =>
  trackEvent(GA4_EVENTS.SITE_SEARCH, {
    event_category: 'engagement',
    search_term: searchTerm,
    result_count: resultCount
  });

export const trackFeedbackSubmit = (rating?: number, commentLength?: number) =>
  trackEvent(GA4_EVENTS.FEEDBACK_SUBMIT, {
    event_category: 'support',
    rating: rating,
    comment_length: commentLength
  });

// ===== ERROR & TECHNICAL EVENTS =====

export const trackTechError = (
  errorType: string,
  errorLocation: string,
  errorMessage?: string
) =>
  trackEvent(GA4_EVENTS.TECH_ERROR, {
    event_category: 'error',
    error_type: errorType,
    error_location: errorLocation,
    error_message: errorMessage?.substring(0, 100)
  });

export const trackAPIError = (
  endpoint: string,
  statusCode: number,
  errorMessage?: string
) =>
  trackEvent(GA4_EVENTS.API_ERROR, {
    event_category: 'error',
    error_type: 'api_error',
    api_endpoint: endpoint,
    status_code: statusCode,
    error_message: errorMessage?.substring(0, 100)
  });

export const trackJourneyDropOff = (stepName: string, userJourney: string) =>
  trackEvent(GA4_EVENTS.JOURNEY_DROP_OFF, {
    event_category: 'behavior',
    drop_off_step: stepName,
    user_journey: userJourney,
    journey_stage: 'drop_off'
  });

// ===== UTILITY EVENTS =====

export const trackPageView = (
  pageName: string,
  userJourney?: string,
  additionalParams?: JobSpaceEventParams
) =>
  trackEvent('page_view', {
    page_title: pageName,
    user_journey: userJourney,
    ...additionalParams
  });

export const trackScrollDepth = (depthPercent: number, pageName: string) =>
  trackEvent('scroll_depth', {
    event_category: 'engagement',
    scroll_depth_percent: depthPercent,
    page_title: pageName
  });

export const trackTimeOnPage = (pageName: string, timeSpentSeconds: number) =>
  trackEvent('time_on_page', {
    event_category: 'engagement',
    page_title: pageName,
    time_spent_seconds: timeSpentSeconds,
    engagement_level:
      timeSpentSeconds > 60 ? 'high' : timeSpentSeconds > 30 ? 'medium' : 'low'
  });
export type GAEventParams = Record<string, unknown>;

/**
 * Safely dispatch a GA4 event using gtag.
 * If gtag isn't available (e.g., during SSR), the event is logged to the console.
 */
export function trackEvent(
  eventName: string,
  params: GAEventParams = {}
): void {
  if (typeof window !== 'undefined' && typeof window.gtag === 'function') {
    window.gtag('event', eventName, params);
  } else {
    console.debug(`gtag not found. Event '${eventName}' not sent`, params);
  }
}

/** Primary Conversion Events */
export const trackFreeTrialSignup = () =>
  trackEvent('sign_up', { method: 'website' });

export const trackCVGeneration = () =>
  trackEvent('generate_cv', { tool_type: 'cv_builder', user_type: 'free' });

export const trackATSCheckCompletion = () =>
  trackEvent('ats_check_complete', {
    tool_type: 'ats_checker',
    user_type: 'free'
  });

export const trackCoverLetterGeneration = () =>
  trackEvent('generate_cover_letter', {
    tool_type: 'cover_letter',
    user_type: 'free'
  });

/** Secondary Events */
export const trackNewsletterSignup = () =>
  trackEvent('newsletter_signup', { method: 'website' });

export const trackDemoRequest = () =>
  trackEvent('request_quote', { item_type: 'demo' });

