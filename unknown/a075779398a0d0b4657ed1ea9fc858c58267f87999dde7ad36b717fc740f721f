// app/api/credits/purchase/route.ts
import { creditPackages } from '@/lib/credit-packages';
import { createClient } from '@/app/utils/supabase/server';
import { NextResponse } from 'next/server';
import Stripe from 'stripe';

const stripe = new Stripe(process.env.STRIPE_SECRET_KEY!, {
  apiVersion: '2025-02-24.acacia'
});

interface PurchaseRequestBody {
  packageId: string;
  returnUrl?: string;
}

export async function POST(req: Request) {
  try {
    const { packageId, returnUrl } = (await req.json()) as PurchaseRequestBody;

    const supabase = await createClient();

    const {
      data: { user },
      error: userError
    } = await supabase.auth.getUser();

    if (userError || !user) {
      return NextResponse.json(
        { error: 'Unauthorized - Please sign in' },
        { status: 401 }
      );
    }

    const creditPackage = creditPackages.find((pkg) => pkg.id === packageId);
    if (!creditPackage) {
      return NextResponse.json(
        { error: 'Invalid package selected' },
        { status: 400 }
      );
    }

    const stripeSession = await stripe.checkout.sessions.create({
      payment_method_types: ['card'],
      line_items: [
        {
          price_data: {
            currency: 'gbp',
            product_data: {
              name: `${creditPackage.name} Credit Package`,
              description: `${creditPackage.credits} Credits`
            },
            unit_amount: Math.round(creditPackage.price * 100)
          },
          quantity: 1
        }
      ],
      mode: 'payment',
      success_url: `${process.env.NEXT_PUBLIC_URL}/credits/success?session_id={CHECKOUT_SESSION_ID}`,
      cancel_url: `${process.env.NEXT_PUBLIC_URL}/credits/cancel`,
      metadata: {
        userId: user.id,
        credits: creditPackage.credits.toString(),
        packageId,
        returnUrl: returnUrl || '' // Now properly typed
      }
    });

    const { error: paymentError } = await supabase.from('payments').insert({
      user_id: user.id,
      amount: Math.round(creditPackage.price * 100),
      credits: creditPackage.credits,
      status: 'pending',
      stripe_session_id: stripeSession.id
    });

    if (paymentError) {
      console.error('Payment record error:', paymentError);
      return NextResponse.json(
        { error: 'Failed to record payment' },
        { status: 500 }
      );
    }

    return NextResponse.json({ sessionId: stripeSession.id });
  } catch (error) {
    console.error('Stripe session error:', error);
    return NextResponse.json(
      { error: 'Failed to create checkout session' },
      { status: 500 }
    );
  }
}
