import React from 'react';
import { ThemedButton } from '@/components/ui/themed-button';

interface ATSAnalysisButtonProps {
  onClick: () => void;
  disabled: boolean;
}

export const ATSAnalysisButton: React.FC<ATSAnalysisButtonProps> = ({
  onClick,
  disabled
}) => {
  return (
    <ThemedButton
      onClick={onClick}
      disabled={disabled}
      variant="primary"
      size="md"
      className="mb-8"
    >
      Perform ATS Analysis
    </ThemedButton>
  );
};
